<?php
#region region DOCS

/** @var Inventario[] $inventarios */
/** @var Contenedor[] $contenedores */
/** @var Activo[] $activos */
/** @var Trabajador[] $trabajadores */
/** @var int|null $filtro_contenedor */
/** @var string|null $filtro_activo_texto */
/** @var int|null $filtro_en_contenedor */
/** @var int|null $filtro_trabajador */
/** @var bool $filtros_aplicados */

use App\classes\Inventario;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Trabajador;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Inventario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Inventario</h4>
				<p class="mb-0 text-muted">Administra el inventario del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="sinventario" class="btn btn-danger me-2"><i class="fa fa-minus-circle fa-fw me-1"></i> Sacar de contenedor</a>
				<a href="iinventario" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Agregar a contenedor</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FILTER PANEL ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Filtros
				</h4>
			</div>
			<div class="panel-body">
				<form action="linventario" method="POST" id="filter-form">
					<div class="row">
						<div class="col-md-3">
							<div class="mb-3">
								<label for="contenedor" class="form-label">Contenedor:</label>
								<select class="form-select" id="contenedor" name="contenedor">
									<option value="">-- Todos los contenedores --</option>
									<?php foreach ($contenedores as $contenedor): ?>
										<option value="<?php echo $contenedor->getId(); ?>" <?php echo ($filtro_contenedor == $contenedor->getId()) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="activo_texto" class="form-label">Activo:</label>
								<input type="text" class="form-control" id="activo_texto" name="activo_texto" placeholder="Buscar por nombre de activo" value="<?php echo htmlspecialchars($filtro_activo_texto ?? ''); ?>">
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="en_contenedor" class="form-label">Estado:</label>
								<select class="form-select" id="en_contenedor" name="en_contenedor">
									<option value="">-- Todos --</option>
									<option value="1" <?php echo ($filtro_en_contenedor == 1) ? 'selected' : ''; ?>>Dentro del contenedor</option>
									<option value="0" <?php echo ($filtro_en_contenedor == 0) ? 'selected' : ''; ?>>Fuera del contenedor</option>
								</select>
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="trabajador" class="form-label">Trabajador Asignado:</label>
								<select class="form-select" id="trabajador" name="trabajador">
									<option value="">-- Todos --</option>
									<?php foreach ($trabajadores as $trabajador): ?>
										<option value="<?php echo $trabajador->getId(); ?>" <?php echo ($filtro_trabajador == $trabajador->getId()) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($trabajador->getCedula() . ' - ' . $trabajador->getNombreCompleto()); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12 text-end">
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-search fa-fw me-1"></i> Filtrar
							</button>
							<a href="linventario" class="btn btn-default">
								<i class="fa fa-redo me-1"></i> Limpiar Filtros
							</a>
						</div>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FILTER PANEL ?>
		
		<?php #region region PANEL INVENTARIO ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h5 class="panel-title">
					Listado de Inventario
				</h5>
				<div class="ms-auto">
					<?php if ($filtros_aplicados): ?>
						<form method="POST" action="export_inventario" style="display: inline;">
							<input type="hidden" name="contenedor" value="<?php echo htmlspecialchars($filtro_contenedor ?? ''); ?>">
							<input type="hidden" name="activo_texto" value="<?php echo htmlspecialchars($filtro_activo_texto ?? ''); ?>">
							<input type="hidden" name="en_contenedor" value="<?php echo htmlspecialchars($filtro_en_contenedor ?? ''); ?>">
							<input type="hidden" name="trabajador" value="<?php echo htmlspecialchars($filtro_trabajador ?? ''); ?>">
							<button type="submit" class="btn btn-sm btn-primary me-2" title="Exportar a Excel">
								<i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
							</button>
						</form>
					<?php endif; ?>
					<a href="prestar_activos" class="btn btn-sm btn-danger me-2"><i class="fa fa-minus-circle fa-fw me-1"></i> Prestar activos</a>
					<a href="regresar_activos" class="btn btn-sm btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Regresar activos</a>
				</div>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE INVENTARIO ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center">Acciones</th>
						<th class="text-center">Contenedor</th>
						<th class="text-center">Activo</th>
						<th class="text-center">Marca</th>
						<th class="text-center">Modelo</th>
						<th class="text-center">Número Serie</th>
						<th class="text-center">Estado</th>
						<th class="text-center">Trabajador Asignado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="inventario-table-body">
					<?php if ($filtros_aplicados): ?>
						<?php foreach ($inventarios as $inventario): ?>
							<?php 
								// Get contenedor and activo information
								$contenedor = null;
								$activo = null;
								try {
									if ($inventario->getId_contenedor()) {
										$contenedor = Contenedor::get($inventario->getId_contenedor(), $conexion);
									}
									if ($inventario->getId_activo()) {
										$activo = Activo::get($inventario->getId_activo(), $conexion);
									}
								} catch (Exception $e) {
									// Handle error silently
								}
							?>
							<tr data-inventario-id="<?php echo $inventario->getId(); ?>">
								<td class="text-center">
									<?php if ($activo): ?>
									<button type="button" class="btn btn-xs btn-info me-1 btn-ver-imagenes"
									        title="Ver Imágenes"
									        data-activoid="<?php echo $activo->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
										<i class="fa fa-images"></i>
									</button>
									<?php endif; ?>
								</td>
								<td><?php echo $contenedor ? htmlspecialchars($contenedor->getDescripcion()) : 'N/A'; ?></td>
								<td><?php echo $activo ? htmlspecialchars($activo->getDescripcion()) : 'N/A'; ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivo_marca() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivo_modelo() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivoNumeroSerie() ?? 'N/A'); ?></td>
								<td class="text-center">
									<?php if ($inventario->getEn_contenedor() == 1): ?>
										<span class="badge bg-success">Dentro del contenedor</span>
									<?php else: ?>
										<span class="badge bg-danger">Fuera del contenedor</span>
									<?php endif; ?>
								</td>
								<td>
									<?php echo $inventario->getTrabajador_nombre() ? htmlspecialchars($inventario->getTrabajador_cedula() . ' - ' . $inventario->getTrabajador_nombre()) : '-- No asignado --'; ?>
								</td>
							</tr>
						<?php endforeach; ?>
						<?php if (empty($inventarios)): ?>
							<tr>
								<td colspan="8" class="text-center">No se encontraron resultados para los filtros aplicados.</td>
							</tr>
						<?php endif; ?>
					<?php else: ?>
						<tr>
							<td colspan="8" class="text-center">Aplique filtros para ver resultados.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE INVENTARIO ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL INVENTARIO ?>
	</div>
	<!-- END #content -->
	
	
	<!-- Modal for displaying images -->
	<div class="modal fade" id="imagenes-modal" tabindex="-1" role="dialog" aria-labelledby="imagenes-modal-label" aria-hidden="true">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="imagenes-modal-label">Imágenes del Activo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="imagenes-container" class="d-flex flex-wrap gap-2">
						<!-- Images will be loaded here -->
					</div>
					<div id="no-imagenes-message" class="text-center p-3 d-none">
						<p>Este activo no tiene imágenes asociadas.</p>
					</div>
					<div id="loading-imagenes" class="text-center p-3">
						<span class="spinner-border spinner-border-sm"></span> Cargando imágenes...
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // --- Handle Filter Form Submission ---
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            filterForm.addEventListener('submit', function (event) {
                // Get all filter values
                const contenedor   = document.getElementById('contenedor').value;
                const activoTexto  = document.getElementById('activo_texto').value.trim();
                const enContenedor = document.getElementById('en_contenedor').value;
                const trabajador   = document.getElementById('trabajador').value;
                
                // Check if at least one filter is selected
                if (!contenedor && !activoTexto && !enContenedor && !trabajador) {
                    // Prevent form submission
                    event.preventDefault();
                    
                    // Show SweetAlert message
					showSweetAlertError('Atención', 'Debe escoger algún filtro')
                    return false;
                }
                
                // Allow form submission if at least one filter is selected
                return true;
            });
        }
        
        // --- Handle Image Viewing ---
        const tableBody          = document.getElementById('inventario-table-body');
        const imagenesModal      = document.getElementById('imagenes-modal');
        const imagenesModalLabel = document.getElementById('imagenes-modal-label');
        const imagenesContainer  = document.getElementById('imagenes-container');
        const noImagenesMessage  = document.getElementById('no-imagenes-message');
        const loadingImagenes    = document.getElementById('loading-imagenes');
        
        // Initialize the modal
        const modal = new bootstrap.Modal(imagenesModal);
        
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const verImagenesButton = event.target.closest('.btn-ver-imagenes');
                
                // --- Handle Ver Imagenes Click ---
                if (verImagenesButton) {
                    event.preventDefault();
                    const activoId = verImagenesButton.dataset.activoid;
                    const descripcion = verImagenesButton.dataset.descripcion || 'Activo';
                    
                    // Update modal title
                    imagenesModalLabel.textContent = `Imágenes del Activo: ${descripcion}`;
                    
                    // Clear previous images
                    imagenesContainer.innerHTML = '';
                    
                    // Show loading indicator
                    loadingImagenes.classList.remove('d-none');
                    noImagenesMessage.classList.add('d-none');
                    
                    // Show the modal
                    modal.show();
                    
                    // Fetch images for this activo
                    fetch(`get_activo_imagenes?id_activo=${activoId}`)
                        .then(response => response.json())
                        .then(data => {
                            // Hide loading indicator
                            loadingImagenes.classList.add('d-none');
                            
                            if (data.length === 0) {
                                // Show no images message
                                noImagenesMessage.classList.remove('d-none');
                            } else {
                                // Display the images
                                data.forEach(imagen => {
                                    const imgContainer = document.createElement('div');
                                    imgContainer.className = 'image-item';
                                    
                                    const img = document.createElement('img');
                                    img.src = `resources/uploads/activos/${imagen.nombre_archivo}`;
                                    img.alt = `Imagen ${imagen.id}`;
                                    img.className = 'img-thumbnail';
                                    img.style.maxWidth = '200px';
                                    img.style.height = 'auto';
                                    
                                    imgContainer.appendChild(img);
                                    imagenesContainer.appendChild(imgContainer);
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching images:', error);
                            loadingImagenes.classList.add('d-none');
                            imagenesContainer.innerHTML = '<div class="alert alert-danger">Error al cargar las imágenes</div>';
                        });
                }
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
